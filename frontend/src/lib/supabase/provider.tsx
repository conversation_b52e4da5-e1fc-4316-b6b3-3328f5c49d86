// frontend/src/lib/supabase/provider.tsx
'use client'

import { createContext, useContext, useState, useEffect } from 'react'
import { supabase } from './client'
import { SupabaseClient, User, Session } from '@supabase/supabase-js';

// Define interface for the context value
interface SupabaseContextValue {
  supabase: SupabaseClient;
  getCurrentUser: () => Promise<User | null>;
  // Add user and session properties to match usage in components
  user?: User | null;
  session?: Session | null;
  isLoading?: boolean;
}

const Context = createContext<SupabaseContextValue | undefined>(undefined);

export function SupabaseProvider({ children }: { children: React.ReactNode }) {
  // Add state for user and session
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Helper to fetch current authenticated user
  const getCurrentUser = async (): Promise<User | null> => {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return user;
  };

  // Set up auth state listener
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (session) {
          setUser(session.user);
          setSession(session);
        } else {
          setUser(null);
          setSession(null);
        }
        setIsLoading(false);
      }
    );

    // Initial session check
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user || null);
      setSession(session);
      setIsLoading(false);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Ensure the value provided matches the interface
  const contextValue: SupabaseContextValue = {
    supabase,
    getCurrentUser,
    user,
    session,
    isLoading,
  };

  return (
    <Context.Provider value={contextValue}>
      {children}
    </Context.Provider>
  )
}

// Update hook return type
export function useSupabase(): SupabaseContextValue {
  const context = useContext(Context)
  if (context === undefined) {
    throw new Error('useSupabase must be used inside SupabaseProvider')
  }
  return context
}
