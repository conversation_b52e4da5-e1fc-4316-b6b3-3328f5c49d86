import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Redis } from '@upstash/redis';
import { Database, Json } from '@/lib/supabase/database.types';

// Define interfaces for tables not in the generated types
interface TenantQuota {
  id: string;
  tenant_id: string;
  max_daily_uploads: number;
  max_monthly_uploads: number;
  max_document_size_mb: number;
  max_concurrent_processing: number;
  plan_tier: string;
  updated_at: string | null;
}

interface ResourceUsage {
  id?: string;
  tenant_id: string;
  usage_type: string;
  usage_count?: number;
  resource_size_bytes?: number;
  period_start: string;
  period_end: string;
  created_at?: string;
}

// Initialize Redis client for rate limiting only when available
let redis: Redis | null = null;

function getRedisClient(): Redis | null {
  // Don't initialize during build time or if env vars are missing
  if (typeof window === 'undefined' && (!process.env.UPSTASH_REDIS_URL || !process.env.UPSTASH_REDIS_TOKEN)) {
    return null;
  }

  if (!redis && process.env.UPSTASH_REDIS_URL && process.env.UPSTASH_REDIS_TOKEN) {
    try {
      redis = new Redis({
        url: process.env.UPSTASH_REDIS_URL,
        token: process.env.UPSTASH_REDIS_TOKEN,
      });
    } catch (error) {
      console.error('Failed to initialize Upstash Redis client:', error);
      redis = null;
    }
  }

  return redis;
}

export class RateLimitService {
  private supabase: SupabaseClient<Database>;

  constructor(supabaseClient: SupabaseClient<Database>) {
    this.supabase = supabaseClient;
  }

  /**
   * Check if tenant has exceeded their upload limits
   */
  async canUploadDocument(tenantId: string, fileSizeBytes: number): Promise<{ allowed: boolean; reason?: string }> {
    // 1. Get tenant quota
    const { data: quota, error: quotaError } = await (this.supabase as any)
      .from('tenant_quotas')
      .select('*')
      .eq('tenant_id', tenantId)
      .single() as { data: TenantQuota | null, error: any };

    if (quotaError || !quota) {
      console.error('Error fetching tenant quota:', quotaError);
      // Default to allowing upload if we can't fetch quota
      return { allowed: true };
    }

    // We now know quota is not null

    // 2. Check file size limit
    const fileSizeMB = fileSizeBytes / (1024 * 1024);
    if (fileSizeMB > quota.max_document_size_mb) {
      return {
        allowed: false,
        reason: `Document size (${fileSizeMB.toFixed(2)}MB) exceeds the limit (${quota.max_document_size_mb}MB)`
      };
    }

    // 3. Check daily upload limit using Redis for fast access (if available)
    const redisClient = getRedisClient();
    if (redisClient) {
      const today = new Date().toISOString().split('T')[0];
      const dailyUploadsKey = `tenant:${tenantId}:uploads:daily:${today}`;

      const dailyUploads = Number(await redisClient.get(dailyUploadsKey)) || 0;
      if (dailyUploads >= quota.max_daily_uploads) {
        return {
          allowed: false,
          reason: `Daily upload limit (${quota.max_daily_uploads}) reached`
        };
      }

      // 4. Check monthly upload limit
      const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM
      const monthlyUploadsKey = `tenant:${tenantId}:uploads:monthly:${currentMonth}`;

      const monthlyUploads = Number(await redisClient.get(monthlyUploadsKey)) || 0;
      if (monthlyUploads >= quota.max_monthly_uploads) {
        return {
          allowed: false,
          reason: `Monthly upload limit (${quota.max_monthly_uploads}) reached`
        };
      }

      // 5. Check concurrent processing limit
      const concurrentProcessingKey = `tenant:${tenantId}:processing:concurrent`;
      const concurrentProcessing = Number(await redisClient.get(concurrentProcessingKey)) || 0;

      if (concurrentProcessing >= quota.max_concurrent_processing) {
        return {
          allowed: false,
          reason: `Maximum concurrent document processing (${quota.max_concurrent_processing}) reached`
        };
      }
    }

    return { allowed: true };
  }

  /**
   * Increment usage counters when a document is uploaded
   */
  async trackDocumentUpload(tenantId: string, fileSizeBytes: number): Promise<void> {
    // 1. Increment Redis counters (if available)
    const redisClient = getRedisClient();
    if (redisClient) {
      const today = new Date().toISOString().split('T')[0];
      const dailyUploadsKey = `tenant:${tenantId}:uploads:daily:${today}`;

      // Set TTL for 48 hours to ensure it covers the full day
      await redisClient.incr(dailyUploadsKey);
      await redisClient.expire(dailyUploadsKey, 60 * 60 * 48);

      const currentMonth = new Date().toISOString().slice(0, 7);
      const monthlyUploadsKey = `tenant:${tenantId}:uploads:monthly:${currentMonth}`;

      // Set TTL for 35 days to ensure it covers the full month
      await redisClient.incr(monthlyUploadsKey);
      await redisClient.expire(monthlyUploadsKey, 60 * 60 * 24 * 35);
    }

    // 2. Track in database for analytics and reporting
    const now = new Date();
    const periodStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const periodEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);

    await (this.supabase as any).from('resource_usage').insert({
      tenant_id: tenantId,
      usage_type: 'upload',
      resource_size_bytes: fileSizeBytes,
      period_start: periodStart.toISOString(),
      period_end: periodEnd.toISOString()
    });
  }

  /**
   * Track document processing start
   */
  async trackProcessingStart(tenantId: string): Promise<void> {
    const redisClient = getRedisClient();
    if (redisClient) {
      const concurrentProcessingKey = `tenant:${tenantId}:processing:concurrent`;
      await redisClient.incr(concurrentProcessingKey);
    }
  }

  /**
   * Track document processing completion
   */
  async trackProcessingComplete(tenantId: string): Promise<void> {
    const redisClient = getRedisClient();
    if (redisClient) {
      const concurrentProcessingKey = `tenant:${tenantId}:processing:concurrent`;
      const count = await redisClient.decr(concurrentProcessingKey);

      // Ensure counter never goes below 0
      if (count < 0) {
        await redisClient.set(concurrentProcessingKey, 0);
      }
    }
  }
}
