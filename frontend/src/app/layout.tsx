// frontend/src/app/layout.tsx
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster } from 'sonner';
import "@copilotkit/react-ui/styles.css"; // Add CopilotKit styles
import { SupabaseProvider } from "@/lib/supabase/provider";
import { SecurityProvider } from "@/lib/security";
import { ReactNode } from "react";
import { CopilotKit } from "@copilotkit/react-core";
import { FingerprintJSClientProvider } from '@/components/providers/fpjs-provider'; // <-- Import the new provider
import { SessionProvider } from '@/contexts/SessionContext';
import { UserProvider } from '@/contexts/UserContext';
import { MonitoringInitializer } from '@/lib/monitoring/monitoring-initializer';
import ConnectionStatusBannerWrapper from '@/components/ui/connection-status-banner-wrapper';

// Force dynamic rendering for the entire app since we use SessionProvider
export const dynamic = 'force-dynamic';

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Pi Lawyer AI",
  description: "Personal Injury Lawyer AI Assistant",
};

export default function RootLayout({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head />
      <body className={`${inter.className} min-h-screen bg-background antialiased`}>
        {/* Monitoring system for error reporting */}
        <MonitoringInitializer>
          {/* Reordered providers so FingerprintJSClientProvider wraps those that rely on useVisitorData */}
          <FingerprintJSClientProvider>
            <SessionProvider>
              {/* Show global connection status banner inside SessionProvider */}
              <ConnectionStatusBannerWrapper />
              <UserProvider>
                <SupabaseProvider>
                  <SecurityProvider>
                    <CopilotKit runtimeUrl="/api/copilotkit">
                      <Toaster richColors closeButton />
                      {children}
                    </CopilotKit>
                  </SecurityProvider>
                </SupabaseProvider>
              </UserProvider>
            </SessionProvider>
          </FingerprintJSClientProvider>
        </MonitoringInitializer>
      </body>
    </html>
  );
}
