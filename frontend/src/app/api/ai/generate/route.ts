// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { withAuth, UserRole } from '@/lib/auth/server-exports';
import OpenAI from 'openai';

/**
 * API route to generate text using OpenAI models
 * Requires authentication and supports role-based access control
 */
export const POST = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff, UserRole.Client], // Allow all authenticated users
  async (req: NextRequest, user: any, supabase: any) => {
  try {
    const { prompt, model = 'gpt-4o', temperature = 0.7, maxTokens = 1000 } = await req.json();

    if (!prompt) {
      return NextResponse.json({ error: 'Prompt is required' }, { status: 400 });
    }

    // Check for OpenAI API key
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      return NextResponse.json({
        error: 'OpenAI API key is not configured'
      }, { status: 500 });
    }

    // Initialize OpenAI client
    const openai = new OpenAI({
      apiKey: apiKey,
    });

    // Call OpenAI API
    const response = await openai.chat.completions.create({
      model: model,
      messages: [
        { role: 'system', content: 'You are AiLex, a helpful legal assistant for attorneys and their clients.' },
        { role: 'user', content: prompt }
      ],
      temperature: temperature,
      max_tokens: maxTokens,
    });

    // Extract the generated text
    const generatedText = response.choices[0]?.message?.content || '';

    return NextResponse.json({
      success: true,
      text: generatedText,
      usage: response.usage
    });
  } catch (error: any) {
    console.error('OpenAI API error:', error);

    // Handle rate limits and other OpenAI-specific errors
    const statusCode = error.status || 500;
    const message = error.message || 'An error occurred while generating text';

    return NextResponse.json({
      error: message,
      type: error.type,
      code: error.code
    }, { status: statusCode });
  }
});
