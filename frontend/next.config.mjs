import path from 'path';
import { fileURLToPath } from 'url';
import { createRequire } from 'module';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const require = createRequire(import.meta.url);

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // Allow production builds to complete even with ESLint warnings
    ignoreDuringBuilds: true,
    // Only fail on errors, not warnings
    dirs: ['src']
  },
  experimental: {
    serverActions: {
      bodySizeLimit: '2mb',
      allowedOrigins: ['localhost:3000']
    },
    optimizePackageImports: ['@copilotkit/react-ui', '@copilotkit/react-core'],
  },
  // Disable source maps in development to avoid file not found errors
  productionBrowserSourceMaps: false,
  env: {
    // Load environment variables from root .env file
    ...dotenv.config({ path: path.join(__dirname, '../.env') }).parsed
  },
  webpack: (config) => {
    // Required for CopilotKit
    if (!config.resolve) {
      config.resolve = {};
    }
    if (!config.resolve.fallback) {
      config.resolve.fallback = {};
    }

    config.resolve.fallback = {
      ...config.resolve.fallback,
      crypto: require.resolve('crypto-browserify'),
      stream: require.resolve('stream-browserify'),
      buffer: require.resolve('buffer'),
    };

    return config;
  },
  distDir: '.next',
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: `
              script-src 'self' 'unsafe-eval' 'unsafe-inline'
              https://anwefmklplkjxkmzpnva.supabase.co
              https://challenges.cloudflare.com
              *.fpjs.io *.fpcdn.io fpnpmcdn.net
              https://vercel.live;
              connect-src 'self'
              https://anwefmklplkjxkmzpnva.supabase.co
              wss://anwefmklplkjxkmzpnva.supabase.co
              https://api.cloud.copilotkit.ai
              *.fpjs.io *.fpcdn.io
              https://vercel.live;
            `.replace(/\s{2,}/g, ' ').trim()
          }
        ]
      }
    ]
  }
}

export default nextConfig
